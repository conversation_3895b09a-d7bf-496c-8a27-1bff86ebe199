{"main": {"id": "86b220b9c155ccbc", "type": "split", "children": [{"id": "afa0241f36a56e1e", "type": "tabs", "children": [{"id": "e3272ecd75ea303c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "01-人工记录输入层/记录界面/日记/2025/07-July/2025-07-31.md", "mode": "preview", "source": true}, "icon": "lucide-file", "title": "2025-07-31"}}, {"id": "cd038ee6f0a49253", "type": "leaf", "state": {"type": "markdown", "state": {"file": "01-人工记录输入层/Obsidian模板库/智能动态日记模板.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "智能动态日记模板"}}]}], "direction": "vertical"}, "left": {"id": "40566c575f7b5c27", "type": "split", "children": [{"id": "85c28abdddcd1afa", "type": "tabs", "children": [{"id": "0d03f3bb77118425", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "733494c9a2c8bedf", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "077c880d6e4c1196", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "f2de600e74de1f01", "type": "split", "children": [{"id": "1782e32a5ad0cc00", "type": "tabs", "dimension": 25, "children": [{"id": "c8ba82df5b2da2e0", "type": "leaf", "state": {"type": "backlink", "state": {"file": "v2.0优化版/01-人工记录输入层/记录界面/日记/2025/2025-07-16.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "2025-07-16 的反向链接列表"}}, {"id": "b85c34f619f87aef", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "v2.0优化版/01-人工记录输入层/记录界面/日记/README.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "README 的出链列表"}}, {"id": "971f6247a9deb570", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "42196ed70a55d249", "type": "leaf", "state": {"type": "calendar", "state": {}, "icon": "calendar-with-checkmark", "title": "Calendar"}}, {"id": "974eb62ffc6cd911", "type": "leaf", "state": {"type": "custom-frames-💰-财务记录", "state": {}, "icon": "lucide-wallet", "title": "💰 财务记录"}}, {"id": "360546e357965a7c", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "新标签页"}}, {"id": "16e3e1e25afc0b2b", "type": "leaf", "state": {"type": "empty", "state": {}, "icon": "lucide-file", "title": "新标签页"}}, {"id": "9fe7092833619728", "type": "leaf", "state": {"type": "graph-analysis", "state": {}, "icon": "GA-ICON", "title": "Graph Analysis"}}], "currentTab": 3}, {"id": "032fe951a40762f3", "type": "tabs", "dimension": 25, "children": [{"id": "b943e01ed54d1b2f", "type": "leaf", "state": {"type": "graph", "state": {}, "icon": "lucide-git-fork", "title": "关系图谱"}}]}, {"id": "79ba825213cf9649", "type": "tabs", "dimension": 50, "children": [{"id": "14e20999a8071674", "type": "leaf", "state": {"type": "mermaid-toolbar-view", "state": {}, "icon": "trident-custom", "title": "Mermaid Toolbar"}}, {"id": "bf4a7760684a5474", "type": "leaf", "state": {"type": "outline", "state": {"file": "01-人工记录输入层/记录界面/日记/2025/07-July/2025-07-31.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "2025-07-31 的大纲"}}], "currentTab": 1}], "direction": "horizontal", "width": 391.5}, "left-ribbon": {"hiddenItems": {"graph:查看关系图谱": false, "cmdr:� 今日收入": false, "cmdr:📅 补录支出": false, "switcher:打开快速切换": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "daily-notes-editor:Open Daily Note Editor": false, "templater-obsidian:Templater": false, "obsidian42-brat:BRAT": false, "cmdr:📈 补录收入": false, "obsidian-kanban:创建新看板": false, "cmdr:⚡ 一键财务记录": false, "cmdr:💸 今日支出": false, "workspaces:管理工作区布局": false, "mermaid-tools:Open Mermaid Toolbar": false, "periodic-notes:Open this week": false}}, "active": "e3272ecd75ea303c", "lastOpenFiles": ["01-人工记录输入层/记录界面/日记/2025/07-July/2025-07-30.md", "01-人工记录输入层/记录界面/日记/2025/07-July/2025-07-31.md", "01-人工记录输入层/Obsidian模板库/智能动态日记模板.md", "01-人工记录输入层/Obsidian模板库/时间节点模板.md", "01-人工记录输入层/Obsidian模板库/智能动态日记模板2.1.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/05-架构系统性阅读执行指南.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/01-元框架经验能力.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/03-AI执行提示词-元框架完整版.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/02-元框架多维系统可视化.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/01-AI执行提示词-元框架完整版.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/04-通用模板生成器.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/04-元框架多维系统可视化.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/03-元框架经验能力.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/02-AI执行提示词-元框架完整版.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/01-通用模板生成器.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/通用模板生成器.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/AI执行提示词-元框架完整版.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/元框架经验能力.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构/元框架多维系统可视化.md", "02-AI协作处理层/经验/06-生成AI系统性标准处理架构", "01-人工记录输入层/记录界面/知识库/数据库技术方向信息收集报告.md", "01-人工记录输入层/记录界面/知识库", "02-AI协作处理层/日记系统/信息收集-整理-处理-决策/01-信息收集-方向阶段.md", "02-AI协作处理层/日记系统/信息收集-整理-处理-决策/04-信息收集-决策阶段.md", "02-AI协作处理层/日记系统/信息收集-整理-处理-决策/03-信息收集-实战阶段.md", "02-AI协作处理层/日记系统/信息收集-整理-处理-决策/02-信息收集-权威阶段.md", "02-AI协作处理层/日记系统/信息收集-整理-处理-决策/信息收集标准方案.md", "02-AI协作处理层/日记系统/情绪记录感受处理流程标准方案.md", "02-AI协作处理层/日记系统/信息收集-整理-处理-决策", "02-AI协作处理层/日记系统/信息收集-整理-处理", "QuickAdd脚本/早晨数据传输器.js", "CSS代码片段/auto-collapse-properties.css", "CSS代码片段", "02-AI协作处理层/AI协作处理的记忆库/模块记忆处理库", "02-AI协作处理层/AI协作处理的记忆库", "03-生态系统架构层/系统解释文档"]}