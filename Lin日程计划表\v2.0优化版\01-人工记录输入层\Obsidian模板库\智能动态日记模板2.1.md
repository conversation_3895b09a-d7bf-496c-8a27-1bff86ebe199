# 🌅 <% tp.date.now("YYYY年MM月DD日 dddd") %> 早晨习惯执行记录

> **模板版本**：v2.1
> **创建时间**：<% tp.date.now("YYYY-MM-DD HH:mm:ss") %>
> **第<% tp.date.now("w") %>周 星期<% tp.date.now("dddd") %>**

## ⏰ 早晨时间安排表 (5:30-6:30)

| 时间段       | 任务内容              | 预期时间 | 实际时间                                              | 完成✅                               | 备注                                                    |
| --------- | ----------------- | ---- | ------------------------------------------------- | --------------------------------- | ----------------------------------------------------- |
| 5:30-5:35 | 起床醒神 (呼吸4拍×4组)    | 5分钟  | `INPUT[text(placeholder("__分钟")):actual_time_1]`  | `INPUT[toggle:task_1_wakeup]`     | ____                                                  |
| 5:35-5:40 | 刷牙洗脸烧水 (标准2分钟)    | 5分钟  | `INPUT[text(placeholder("__分钟")):actual_time_2]`  | `INPUT[toggle:task_2_hygiene]`    | ____                                                  |
| 5:40-5:45 | 喝水500ml (慢饮补水)    | 5分钟  | `INPUT[text(placeholder("__分钟")):actual_time_3]`  | `INPUT[toggle:task_3_water]`      | ____                                                  |
| 5:45-5:47 | HIIT动作1: 开合跳20秒X4   | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_4]`  | `INPUT[toggle:task_4_hiit1]`      | ____                                                  |
| 5:47-5:49 | HIIT动作2: 提膝下压20秒X4   | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_5]`  | `INPUT[toggle:task_5_hiit2]`      | ____                                                  |
| 5:49-5:51 | HIIT动作3: 膝下击掌20秒X4   | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_6]`  | `INPUT[toggle:task_6_hiit3]`      | ____                                                  |
| 5:51-5:53 | HIIT动作4: 左右盘踢20秒X4  | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_7]`  | `INPUT[toggle:task_7_hiit4]`      | ____                                                  |
| 5:53-5:55 | HIIT动作5: 对侧提膝20秒X4    | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_8]`  | `INPUT[toggle:task_8_hiit5]`      | ____                                                  |
| 5:55-5:57 | HIIT动作6: 同侧提膝20秒X4   | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_9]`  | `INPUT[toggle:task_9_hiit6]`      | ____                                                  |
| 5:57-5:59 | HIIT动作7: 原地慢跑20秒X4   | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_10]` | `INPUT[toggle:task_10_hiit7]`     | ____                                                  |
| 5:59-6:01 | HIIT动作8: 勾腿跳跃20秒X4    | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_11]` | `INPUT[toggle:task_11_hiit8]`     | ____                                                  |
| 6:01-6:16 | 单车15分钟 (热身→标准→冲刺) | 15分钟 | `INPUT[text(placeholder("__分钟")):actual_time_12]` | `INPUT[toggle:task_12_bike]`      | ____                                                  |
| 6:16-6:20 | 健康记录 (体重+状态)      | 4分钟  | `INPUT[text(placeholder("__分钟")):actual_time_13]` | `INPUT[toggle:task_13_health]`    | 体重`INPUT[text(placeholder("__kg")):morning_weight]`kg |
| 6:20-6:25 | 装水整理 (2L水壶+物品归位)  | 5分钟  | `INPUT[text(placeholder("__分钟")):actual_time_14]` | `INPUT[toggle:task_14_prepare]`   | ____                                                  |
| 6:25-6:28 | 早餐安排 (营养搭配)       | 3分钟  | `INPUT[text(placeholder("__分钟")):actual_time_15]` | `INPUT[toggle:task_15_breakfast]` | ____                                                  |
| 6:28-6:30 | 最终准备 (心理调整+目标确认)  | 2分钟  | `INPUT[text(placeholder("__分钟")):actual_time_16]` | `INPUT[toggle:task_16_final]`     | ____                                                  |

## 🎮 **实时状态面板**

### 🏆 快速操作区

**测试批量更新**：
`BUTTON[complete-all, reset-all]`

```meta-bind-button
label: 🏆 一键完成所有任务
id: complete-all
hidden: true
style: primary
actions:
  - type: updateMetadata
    bindTarget: task_1_wakeup
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_2_hygiene
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_3_water
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_4_hiit1
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_5_hiit2
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_6_hiit3
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_7_hiit4
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_8_hiit5
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_9_hiit6
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_10_hiit7
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_11_hiit8
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_12_bike
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_13_health
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_14_prepare
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_15_breakfast
    evaluate: false
    value: true
  - type: updateMetadata
    bindTarget: task_16_final
    evaluate: false
    value: true
```

```meta-bind-button
label: 🔄 一键重置所有状态
id: reset-all
hidden: true
style: destructive
actions:
  - type: updateMetadata
    bindTarget: task_1_wakeup
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_2_hygiene
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_3_water
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_4_hiit1
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_5_hiit2
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_6_hiit3
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_7_hiit4
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_8_hiit5
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_9_hiit6
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_10_hiit7
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_11_hiit8
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_12_bike
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_13_health
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_14_prepare
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_15_breakfast
    evaluate: false
    value: false
  - type: updateMetadata
    bindTarget: task_16_final
    evaluate: false
    value: false
```

### 📊 早晨习惯完成进度

```dataviewjs
// 获取当前页面的任务完成状态
const tasks = [
  dv.current().task_1_wakeup,
  dv.current().task_2_hygiene,
  dv.current().task_3_water,
  dv.current().task_4_hiit1,
  dv.current().task_5_hiit2,
  dv.current().task_6_hiit3,
  dv.current().task_7_hiit4,
  dv.current().task_8_hiit5,
  dv.current().task_9_hiit6,
  dv.current().task_10_hiit7,
  dv.current().task_11_hiit8,
  dv.current().task_12_bike,
  dv.current().task_13_health,
  dv.current().task_14_prepare,
  dv.current().task_15_breakfast,
  dv.current().task_16_final
];

// 计算完成数量
const completedCount = tasks.filter(task => task === true).length;
const totalTasks = 16;
const completionRate = Math.round((completedCount / totalTasks) * 100);

// 生成进度条
const filledBlocks = Math.floor(completionRate / 5);
const progressBar = "█".repeat(filledBlocks) + "░".repeat(20 - filledBlocks);

// 状态判断
let statusEmoji = "";
let statusText = "";
let energyLevel = "";

if (completionRate >= 90) {
  statusEmoji = "🏆";
  statusText = "完美执行！";
  energyLevel = "⚡⚡⚡";
} else if (completionRate >= 75) {
  statusEmoji = "🌟";
  statusText = "表现优秀！";
  energyLevel = "⚡⚡";
} else if (completionRate >= 50) {
  statusEmoji = "💪";
  statusText = "继续加油！";
  energyLevel = "⚡";
} else {
  statusEmoji = "🔥";
  statusText = "需要努力！";
  energyLevel = "🔋";
}

// 显示结果
dv.paragraph(`🌅 **早晨习惯完成度：** ${completionRate}% ${progressBar}`);
dv.paragraph(`- ✅ 已完成：${completedCount}/${totalTasks} 任务`);
dv.paragraph(`- ${statusEmoji} 状态评价：${statusText}`);
dv.paragraph(`- ${energyLevel} 精力状态：${completionRate >= 75 ? "充沛" : completionRate >= 50 ? "良好" : "需要休息"}`);
```

**早晨执行总结**：

```dataviewjs
// 计算完成任务数
const tasks = [
  dv.current().task_1_wakeup, dv.current().task_2_hygiene, dv.current().task_3_water,
  dv.current().task_4_hiit1, dv.current().task_5_hiit2, dv.current().task_6_hiit3,
  dv.current().task_7_hiit4, dv.current().task_8_hiit5, dv.current().task_9_hiit6,
  dv.current().task_10_hiit7, dv.current().task_11_hiit8, dv.current().task_12_bike,
  dv.current().task_13_health, dv.current().task_14_prepare, dv.current().task_15_breakfast,
  dv.current().task_16_final
];

const completedCount = tasks.filter(task => task === true).length;
const totalTasks = 16;

dv.paragraph(`- **总计划时间**：60分钟`);
dv.paragraph(`- **实际执行时间**：____分钟`);
dv.paragraph(`- **完成任务数**：${completedCount}/${totalTasks}`);
dv.paragraph(`- **整体评价**：____`);
```
---

# 🌟 模块二：早上事宜

## 🌤️ 今日天气 & 快速工具

### 🌤️ 深圳今日天气
<%*
// 实时天气获取
try {
    const wttrUrl = "https://wttr.in/Shenzhen?format=%C+%t+%h+%w";
    const response = await tp.obsidian.requestUrl(wttrUrl);
    const weatherText = response.text.trim();

    if (weatherText && !weatherText.includes("ERROR")) {
        // 解析天气数据
        const tempMatch = weatherText.match(/([+-]?\d+)°?[CF]/);
        const temp = tempMatch ? parseInt(tempMatch[1]) : 25;

        const humidityMatch = weatherText.match(/(\d+)%/);
        const humidity = humidityMatch ? humidityMatch[1] : "未知";

        const windMatch = weatherText.match(/(\d+(?:\.\d+)?)\s*km\/h/);
        const windSpeed = windMatch ? Math.round(parseFloat(windMatch[1])) : 0;

        const parts = weatherText.split(' ');
        const condition = parts[0] || "晴";

        // 穿衣建议
        let clothing = "";
        if (temp <= 10) clothing = "厚外套、毛衣";
        else if (temp <= 20) clothing = "薄外套、长袖";
        else if (temp <= 30) clothing = "短袖、薄衫";
        else clothing = "短袖、防晒";

        // 更精确的天气图标判断
        let icon = "☀️";
        let weatherAlert = "";
        const textLower = condition.toLowerCase();

        if (textLower.includes("rain") || textLower.includes("雨") || textLower.includes("shower") || textLower.includes("drizzle")) {
            icon = "🌧️";
            weatherAlert = "⚠️ **有雨，建议带伞**";
        } else if (textLower.includes("thunder") || textLower.includes("雷")) {
            icon = "⛈️";
            weatherAlert = "⚠️ **雷雨天气，注意安全**";
        } else if (textLower.includes("snow") || textLower.includes("雪")) {
            icon = "❄️";
            weatherAlert = "⚠️ **下雪天气，注意保暖**";
        } else if (textLower.includes("fog") || textLower.includes("mist") || textLower.includes("雾") || textLower.includes("霾")) {
            icon = "🌫️";
            weatherAlert = "⚠️ **能见度较低，出行注意安全**";
        } else if (textLower.includes("cloud") || textLower.includes("多云") || textLower.includes("阴")) {
            icon = "⛅";
        }

        // 智能穿衣建议（考虑天气状况）
        if (temp <= 10) {
            clothing = "厚外套、毛衣、围巾";
        } else if (temp <= 20) {
            clothing = "薄外套、长袖";
            if (weatherAlert) clothing += "、雨具";
        } else if (temp <= 30) {
            clothing = "短袖、薄衫";
            if (weatherAlert) clothing += "、雨伞";
        } else {
            clothing = "短袖、防晒";
            if (weatherAlert) clothing += "、雨伞";
        }

        // 出行建议
        let travelTip = "";
        if (weatherAlert) {
            travelTip = "🚗 **出行提醒**：有降雨，建议带伞，注意路面湿滑";
        } else if (windSpeed > 20) {
            travelTip = "� **出行提醒**：风力较大，注意安全";
        } else if (temp > 35) {
            travelTip = "🌡️ **出行提醒**：高温天气，注意防暑降温";
        } else if (temp < 5) {
            travelTip = "🧊 **出行提醒**：气温较低，注意保暖";
        }

        tR += `> [!info] 🌤️ 深圳实时天气
> **📍 温度**：${temp}°C | ${icon} ${condition}
>
> **💧 湿度**：${humidity}${humidity !== "未知" ? "%" : ""}${windSpeed > 0 ? ` | **💨 风速**：${windSpeed}km/h` : ''}
>
> **👔 穿衣建议**：${clothing}`;

        if (weatherAlert) {
            tR += `
>
> ${weatherAlert}`;
        }

        if (travelTip) {
            tR += `
>
> ${travelTip}`;
        }

        tR += `
>
> **🕐 更新时间**：${new Date().toLocaleString()}`;
    } else {
        tR += `**📍 深圳天气**：____°C | ☀️/⛅/☁️/🌧️ | 穿衣：____

> [!info] 🌤️ 天气获取失败，请手动填写`;
    }
} catch (error) {
    tR += `**📍 深圳天气**：____°C | ☀️/⛅/☁️/🌧️ | 穿衣：____

> [!error] ⚠️ 天气功能暂时不可用`;
}
%>


### 快速链接
**📰 资讯**：[今日头条](https://www.toutiao.com/) | [网易新闻](https://news.163.com/)
**💰 财经**：[雪球](https://xueqiu.com/) | [东方财富](https://www.eastmoney.com/)
**⏰ 效率**：[番茄工作法](https://pomofocus.io/) | [白噪音](https://mynoise.net/)

## 📊 昨日数据回顾

**💰 昨日财务**：收入____元 | 支出____元 | 结余____元
**📋 昨日任务**：完成____项 | 进行中____项 | 未完成____项

## 📋 今日目标提醒（四象限法则）

> [!tip] 💡 重要性-紧急性四象限
> **优先级排序，时刻提醒自己专注重要的事情：**

| 🔥 **重要且紧急** | 📋 **重要不紧急** | ⚡ **紧急不重要** | 🛋️ **不重要不紧急** |
|------------------|------------------|------------------|------------------|
| 1. ____________________ | 1. ____________________ | 1. ____________________ | 1. ____________________ |
| 2. ____________________ | 2. ____________________ | 2. ____________________ | 2. ____________________ |
| 3. ____________________ | 3. ____________________ | 3. ____________________ | 3. ____________________ |

**🎯 今日重点关注（重要不紧急象限）**：
1. ____________________
2. ____________________
3. ____________________

---
# 📊 今日数据录入

##  健康数据记录

### 📏 体重记录 (链接到上方)
> [!info] 🔗 数据链接说明
> 体重在上方"健康记录"任务中填写，这里自动显示

**📅 今日体重**：`=this.morning_weight`kg (来自上方早晨记录)

### 😴 睡眠数据
- **🛏️ 昨晚睡眠时长**：`INPUT[text(placeholder("__小时__分钟")):sleep_duration]`
- **😴 入睡时间**：`INPUT[text(placeholder("__:__")):sleep_start]`
- **🌅 起床时间**：`INPUT[text(placeholder("__:__")):wake_time]`
- **💤 睡眠质量**：`INPUT[inlineSelect(option(很好😴), option(一般😐), option(不好😵)):sleep_quality]`
- **🌙 梦境情况**：`INPUT[inlineSelect(option(无梦😶), option(好梦😊), option(噩梦😰)):dream_status]`

### 🏃 运动数据
- **🚶 今日步数**：`INPUT[text(placeholder("____步")):steps_today]`
- **⏱️ 运动时长**：`INPUT[text(placeholder("__分钟")):exercise_duration]`
- **💪 运动类型**：`INPUT[inlineSelect(option(跑步🏃), option(健身💪), option(瑜伽🧘), option(散步🚶), option(游泳🏊), option(其他)):exercise_type]`
- **🔥 运动强度**：`INPUT[inlineSelect(option(轻度😌), option(中度😊), option(高强度🔥)):exercise_intensity]`
- **😊 运动感受**：`INPUT[inlineSelect(option(很爽😎), option(还行😐), option(累😴)):exercise_feeling]`

### 💧 饮水记录
**目标：每日2000ml**

| 时间段 | 饮水量 | 水质类型 | 完成✅ |
|--------|--------|----------|--------|
| 早晨(6:00-9:00) | `INPUT[text(placeholder("__ml")):water_morning]` | 温开水/茶/咖啡 | ☐ |
| 上午(9:00-12:00) | `INPUT[text(placeholder("__ml")):water_forenoon]` | 白开水/茶 | ☐ |
| 下午(12:00-18:00) | `INPUT[text(placeholder("__ml")):water_afternoon]` | 白开水/饮料 | ☐ |
| 晚上(18:00-22:00) | `INPUT[text(placeholder("__ml")):water_evening]` | 白开水/汤 | ☐ |

**💧 今日饮水总计**：`INPUT[text(placeholder("____ml")):water_total]`

### 🍽️ 饮食记录
**� 三餐记录**：
- **🌅 早餐**：`INPUT[text(placeholder("具体食物")):breakfast_food]` | 时间：`INPUT[text(placeholder("__:__")):breakfast_time]`
- **🌞 午餐**：`INPUT[text(placeholder("具体食物")):lunch_food]` | 时间：`INPUT[text(placeholder("__:__")):lunch_time]`
- **🌙 晚餐**：`INPUT[text(placeholder("具体食物")):dinner_food]` | 时间：`INPUT[text(placeholder("__:__")):dinner_time]`

**🍎 零食记录**：`INPUT[text(placeholder("零食类型和时间")):snacks]`

**💊 补充剂记录**：`INPUT[text(placeholder("维生素/蛋白粉等")):supplements]`


---
# 🧠 情绪感受记录 {-}

### 📝 今日情绪事件（有什么触发情绪的事就简单记一下）

> [!tip]- 💡 情绪事件记录使用说明
> **使用流程**：
> 1. **用户只需填写详细记录** - 想写什么就写什么，把内心想法倒出来
> 2. **AI提取四个维度** - 从详细记录中分析提取脑子想的、身体感受等
> 3. **AI记录操作逻辑** - 说明分析步骤和逻辑依据，便于理解和改进
>
> **标注块说明**：
> - `[!quote]` 灰色 - 用户原创记录
> - `[!abstract]` 青色 - AI提取的四个维度
> - `[!info]` 蓝色 - AI操作指引与逻辑链
>
> 详细使用方法请参考：[[Obsidian标注块(Callout)使用指南]]

#### **事件1**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：*想写什么就写什么，把内心的想法都倒出来...*

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件2**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件3**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件4**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件5**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

#### **事件6**：____________________

> [!quote] 👤 用户原创记录
> **详细记录**：

> [!abstract] 🤖 AI提取的四个维度
> **基于上述详细记录，AI分析提取：**
> - 🧠 脑子想的：____________________
> - 💓 身体感受：____________________
> - 🗣️ 嘴上说的：____________________
> - 🏃 行动上的：____________________

> [!info] 🔧 AI操作指引与逻辑链
> **AI处理逻辑**：
> 1. 分析步骤：____________________
> 2. 提取方法：____________________
> 3. 逻辑依据：____________________
> 4. 操作说明：____________________

> [!abstract] 🤖 AI生成分析内容
> **基于上述记录的深度分析**：

> [!tip] 复杂情绪识别
> 如果遇到难以名状的复杂情绪，可以使用专门的模板：
> **[[情绪识别与调节权威清单]]** - 基于权威心理学理论的情绪识别与调节策略
>
> *需要更多事件记录时，直接复制粘贴上面事件格式即可*

### 🎭 今日主要情绪
**主要情绪**：`INPUT[inlineSelect(option(😊 开心), option(😔 难过), option(😰 焦虑), option(😠 生气), option(😴 疲惫), option(😌 平静), option(🤔 困惑), option(😤 烦躁)):main_emotion]`

### 💭 今日感受总结（AI来完成）
*AI会根据上面的详细记录来分析和总结情绪状态*

---
---
# 💰 财务概览

> [!tip] 💡 使用右侧边栏快速记录
> 点击右侧边栏的"💸 快速支出"或"💰 快速收入"按钮进行记录

### 📊 今日财务数据

**💰 收入总计**：____元 | **💸 支出总计**：____元 | **💵 结余**：____元

### 📈 收入记录

| 时间 | 收入类型 | 金额 | 来源说明 | 备注 |
|------|----------|------|----------|------|

### 📉 支出记录

| 时间 | 支出类型 | 金额 | 具体项目 | 必要性 | 备注 |
|------|----------|------|----------|--------|------|

---
# 📋 配置说明

> **简洁原则**：模块二采用简洁设计，无需额外YAML变量
> **手动填写**：天气、财务、目标等信息直接在对应位置填写
> **保持原有**：模块一的早晨习惯配置保持不变

---
date: "{ date:YYYY-MM-DD }"
display_date: "{ date:YYYY年MM月DD日 dddd }"
created: "{ date }"
week: "{ date:w }"
weekday: "{ date:d }"
tags:
  - 日记2.0
  - 生活习惯
  - 极简版
template_version: 2.1-简洁版
emotion_event_1_thinking: ""
exercise_type: 散步🚶
sleep_quality: 一般😐
dream_status: 无梦😶
sleep_duration: ""
task_1_wakeup: false
task_2_hygiene: false
task_3_water: false
task_4_hiit1: false
task_5_hiit2: false
task_6_hiit3: false
task_7_hiit4: false
task_8_hiit5: false
task_9_hiit6: false
task_10_hiit7: false
task_11_hiit8: false
task_12_bike: false
task_13_health: false
task_14_prepare: false
task_15_breakfast: false
task_16_final: false
morning_weight: ""
---