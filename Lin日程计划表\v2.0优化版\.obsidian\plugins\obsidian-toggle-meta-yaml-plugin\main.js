/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => ToggleMetaYamlPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian = require("obsidian");
var import_state = require("@codemirror/state");
var import_view = require("@codemirror/view");

// src/utils/index.ts
var hideMetaClassName = "toggle-meta-yaml--hide";
function showMetaYaml() {
  document.body.classList.remove(hideMetaClassName);
}
function hideMetaYaml() {
  document.body.classList.add(hideMetaClassName);
}

// src/main.ts
var DEFAULT_SETTINGS = {
  show: true
};
var ToggleMetaYamlPlugin = class extends import_obsidian.Plugin {
  constructor() {
    super(...arguments);
    this.onKeyup = (event, view) => {
      const cursor = view.state.selection.asSingle().main;
      const metaRange = this.getRangeOfMetaYaml(view);
      const editor = this.getEditor();
      if (metaRange.to === metaRange.from)
        return;
      if (event.key === "ArrowDown") {
        if (cursor.from <= metaRange.from) {
          editor == null ? void 0 : editor.setCursor(metaRange.lineEnd + 1);
        }
      }
    };
    this.getEditor = () => {
      let editor = null;
      let markdownView = this.app.workspace.getActiveViewOfType(import_obsidian.MarkdownView);
      if (markdownView) {
        editor = markdownView.editor;
      }
      if (editor === null)
        console.log("can't get editor");
      return editor;
    };
  }
  async onload() {
    console.log(`load ToggleMetaYaml Plugin`);
    await this.loadSettings();
    this.addSettingTab(new ToggleMetaYamlSettingTab(this.app, this));
    this.addCommand({
      id: "obsidian-toggle-meta-yaml-toggle",
      name: "toggle",
      editorCallback: () => {
        this.settings.show = !this.settings.show;
        this.refreshView();
      }
    });
    this.registerEditorExtension([
      import_state.Prec.highest(import_view.EditorView.domEventHandlers({
        "keyup": this.onKeyup
      }))
    ]);
  }
  onunload() {
    this.reset();
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
    await this.saveSettings();
  }
  async saveSettings() {
    await this.saveData(this.settings);
    this.refreshView();
  }
  async reset() {
    this.settings = { ...DEFAULT_SETTINGS };
    await this.saveSettings();
  }
  refreshView() {
    if (this.settings.show) {
      showMetaYaml();
    } else {
      hideMetaYaml();
    }
  }
  getRangeOfMetaYaml(view) {
    const editor = this.getEditor();
    const range = { from: 0, to: 0, lineEnd: 0, lineStart: 0 };
    if (editor) {
      const lineCount = editor.lineCount();
      const docs = [];
      const metaYaml = [];
      const metaYamlBoundaryMatch = /^-{3}$/;
      for (let i = 0; i < lineCount; i++) {
        const string = editor.getLine(i);
        const hasStart = metaYaml.filter((v) => string.match(metaYamlBoundaryMatch)).length === 1;
        const hasEnd = metaYaml.filter((v) => string.match(metaYamlBoundaryMatch)).length === 2;
        if (string.match(metaYamlBoundaryMatch)) {
          if (!metaYaml.length) {
            const start = docs.join("").length + 1;
            range.from = start;
            range.lineStart = docs.length + 1;
            metaYaml.push(string);
          } else if (!hasEnd) {
            metaYaml.push(string);
            const end = range.from + metaYaml.join("").length;
            range.to = end;
            range.lineEnd = metaYaml.length + docs.length;
          } else {
            docs.push(string);
          }
        } else {
          if (hasStart && !hasEnd) {
            metaYaml.push(string);
          } else {
            docs.push(string);
          }
        }
      }
    }
    return range;
  }
};
var ToggleMetaYamlSettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h2", { text: "Toggle Meta Yaml Settings" });
    new import_obsidian.Setting(containerEl).setName("Show Meta Yaml").setDesc("If disable, markdown meta yaml will be hidden. It only work at live preview mode, preview mode don't support currently. Source mode don't need it.").addToggle((component) => component.setValue(this.plugin.settings.show).onChange(async (value) => {
      this.plugin.settings.show = value;
      await this.plugin.saveSettings();
    }));
  }
};

/* nosourcemap */